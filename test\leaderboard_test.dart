import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_quiz_app_project/screens/leaderboard_screen.dart';
import 'package:flutter_quiz_app_project/controllers/data_controller.dart';
import 'package:flutter_quiz_app_project/controllers/theme_provider.dart';
import 'package:flutter_quiz_app_project/services/leaderboard_service.dart';

void main() {
  group('Leaderboard Screen Tests', () {
    setUp(() {
      // Initialize GetX controllers
      Get.put(DataController());
      Get.put(ThemeController());
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('Leaderboard screen displays correctly in light mode', (WidgetTester tester) async {
      // Build the leaderboard screen
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData.light(),
          home: const LeaderboardScreen(),
        ),
      );

      // Wait for the screen to load
      await tester.pumpAndSettle();

      // Verify that the leaderboard title is visible
      expect(find.text('Leaderboard'), findsOneWidget);
      
      // Verify that the refresh button is present
      expect(find.byIcon(Icons.refresh_rounded), findsOneWidget);
      
      // Verify that the edit profile button is present
      expect(find.byIcon(Icons.person_outline_rounded), findsOneWidget);
    });

    testWidgets('Refresh button works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData.light(),
          home: const LeaderboardScreen(),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Find and tap the refresh button
      final refreshButton = find.byIcon(Icons.refresh_rounded);
      expect(refreshButton, findsOneWidget);
      
      await tester.tap(refreshButton);
      await tester.pumpAndSettle();

      // Verify the screen still displays correctly after refresh
      expect(find.text('Leaderboard'), findsOneWidget);
    });

    testWidgets('Edit profile button opens avatar selection dialog', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData.light(),
          home: const LeaderboardScreen(),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Find and tap the edit profile button
      final editProfileButton = find.byIcon(Icons.person_outline_rounded);
      expect(editProfileButton, findsOneWidget);
      
      await tester.tap(editProfileButton);
      await tester.pumpAndSettle();

      // Verify that the avatar selection dialog appears
      expect(find.text('Choose Avatar'), findsOneWidget);
      expect(find.text('Select your profile avatar:'), findsOneWidget);
    });

    testWidgets('Pull to refresh works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData.light(),
          home: const LeaderboardScreen(),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Find the RefreshIndicator and perform pull-to-refresh
      final refreshIndicator = find.byType(RefreshIndicator);
      expect(refreshIndicator, findsOneWidget);

      // Simulate pull-to-refresh gesture
      await tester.fling(refreshIndicator, const Offset(0, 300), 1000);
      await tester.pump();
      
      // Wait for refresh to complete
      await tester.pumpAndSettle();

      // Verify the screen still displays correctly after refresh
      expect(find.text('Leaderboard'), findsOneWidget);
    });

    testWidgets('Avatar selection updates user avatar', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData.light(),
          home: const LeaderboardScreen(),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Open avatar selection dialog
      final editProfileButton = find.byIcon(Icons.person_outline_rounded);
      await tester.tap(editProfileButton);
      await tester.pumpAndSettle();

      // Find and tap on the first available avatar
      final firstAvatar = find.text(LeaderboardService.availableAvatars.first);
      if (firstAvatar.evaluate().isNotEmpty) {
        await tester.tap(firstAvatar);
        await tester.pumpAndSettle();

        // Verify that a success message appears
        expect(find.text('Avatar updated successfully!'), findsOneWidget);
      }
    });

    testWidgets('Leaderboard displays in light mode with proper contrast', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData.light(),
          home: const LeaderboardScreen(),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Verify that text elements are visible (not white on light background)
      final leaderboardTitle = tester.widget<Text>(find.text('Leaderboard'));
      expect(leaderboardTitle.style?.color, isNot(equals(Colors.white)));
    });
  });
}
